import { TRPCError } from "@trpc/server"
import { and, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { RoleE } from "../../../../../../common/src/permissions/roles/helpers/types"
import { db } from "../../../db"
import { eventToGameSchema } from "../../../db/schema/eventToGame.schema"
import { gamesSchema } from "../../../db/schema/games.schema"
import { permissionUserToRoleSchema } from "../../../db/schema/permissionUserToRole.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { publicEventProcedure } from "../../../trpc/procedures/publicEventProcedure"
import { extractGameData, stripExtracted } from "../../_helpers/extractGameData"
import { selectEventUsers } from "../../_selects/select.eventUsers"
import { selectGameData } from "../../_selects/select.gameData"

const EVENT_HOME_GAME_COUNT = 20
const EVENT_HOME_USER_COUNT = 20

export const publicEventExtended = publicEventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, event, isAuthenticated } }) => {
    // For public viewing, we need to check if user can view the event
    const user = loginData || { roles: [] }

    if (!hasPermission(user, "event", "viewPublic", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't view this event",
      })
    }

    // Define what roles can be seen publicly
    const canSee: RoleE[] = ["host", "cohost", "participant"]

    // Only add additional roles if user is authenticated and has approval permissions
    if (
      isAuthenticated &&
      loginData &&
      hasPermission(loginData, "event", "approve", event)
    ) {
      canSee.push("unwelcome")
      canSee.push("requested")
      canSee.push("reserved")
      canSee.push("interested")
    }

    // Get wizard state only if user is authenticated
    const wizzard =
      isAuthenticated && loginData
        ? await db
            .select({
              wizzardState: userToEventSchema.wizzardState,
              share: userToEventSchema.shareGames,
            })
            .from(userToEventSchema)
            .where(
              and(
                eq(userToEventSchema.userId, loginData.id),
                eq(userToEventSchema.eventId, input.eventId),
              ),
            )
            .then((settings) => settings[0])
        : null

    const participants = await db
      .select({ ...selectEventUsers })
      .from(permissionUserToRoleSchema)
      .innerJoin(
        usersSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .where(
        and(
          eq(permissionUserToRoleSchema.subjectId, input.eventId),
          eq(permissionUserToRoleSchema.subject, "event"),
          inArray(permissionUserToRoleSchema.roleId, canSee),
        ),
      )
      .limit(EVENT_HOME_USER_COUNT)
      .then((event) => {
        return event[0]
      })

    const games = await db
      .select({
        ...selectGameData,
        users: eventToGameSchema.users,
        news: eventToGameSchema.lastUpdated,
      })
      .from(eventToGameSchema)
      .innerJoin(gamesSchema, eq(eventToGameSchema.gameId, gamesSchema.id))
      .where(eq(eventToGameSchema.eventId, input.eventId))
      .orderBy(eventToGameSchema.lastUpdated)
      .limit(EVENT_HOME_GAME_COUNT)
      .then(async (games) => {
        return games.map((game) => {
          const { users } = extractGameData(game)

          return {
            ...game,
            ...stripExtracted,
            users,
          }
        })
      })

    return {
      participants,
      wizzard: wizzard ?? null,
      games,
      isAuthenticated,
    }
  })
