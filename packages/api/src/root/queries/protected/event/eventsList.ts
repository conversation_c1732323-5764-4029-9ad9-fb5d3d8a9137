import { SQL, and, eq, gt, inArray, lt, ne, or } from "drizzle-orm"
import { union } from "drizzle-orm/mysql-core"
import { z } from "zod"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { communityToEventSchema } from "../../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../../../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../../../db/schema/userToCommunity.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { selectEvents } from "../../_selects/select.events"
import { getEventCommunities } from "../../_subqueries/event/getEventHosts"
import { hostInfoSchema } from "../../event/eventsListHelper"

export const eventsList = protectedProcedure
  .input(
    z.object({
      communityId: z.number().optional(),
      period: z.enum(["past", "future", "current", "active"]).optional(),
      owned: z.enum(["host", "my", "all"]).optional(), // "my" = means any event where user has any status
    }),
  )
  .query(async ({ input, ctx: { loginData } }) => {
    let selectPeriod: SQL | undefined
    // get all user communities if not provided with specific
    const userCommunities = input.communityId
      ? [input.communityId]
      : await db
          .select({
            id: communitySchema.id,
          })
          .from(communitySchema)
          .innerJoin(
            userToCommunitySchema,
            eq(communitySchema.id, userToCommunitySchema.communityId),
          )
          .where(eq(userToCommunitySchema.userId, loginData.id))
          .then((communities) => communities.map((community) => community.id))

    const selectCommunityOrOpen = input.communityId
      ? inArray(communityToEventSchema.communityId, userCommunities)
      : or(
          inArray(communityToEventSchema.communityId, userCommunities),
          eq(eventSchema.openness, "public"),
          eq(eventSchema.openness, "publicLimited"),
        )

    // =================================================> Period vvv

    // select period "past"
    if (input.period === "past") {
      selectPeriod = lt(eventSchema.starts, new Date())
    }

    // select period "future"
    if (input.period === "future") {
      selectPeriod = gt(eventSchema.starts, new Date())
    }

    // select period "current"
    if (input.period === "current") {
      selectPeriod = and(
        lt(eventSchema.starts, new Date()),
        gt(eventSchema.ends, new Date()),
      )
    }

    // select period "active" (past + future)
    if (input.period === undefined || input.period === "active") {
      selectPeriod = or(
        gt(eventSchema.starts, new Date()),
        and(
          lt(eventSchema.starts, new Date()),
          gt(eventSchema.ends, new Date()),
        ),
      )
    }

    // =================================================> Period ^^^

    // remove hidden events
    const selectWhereBase = ne(eventSchema.state, "hidden")

    const selectWhereCommunityAndPeriod = and(
      selectCommunityOrOpen,
      selectPeriod,
    )

    const eventsCommon = db
      .select(selectEvents)
      .from(eventSchema)
      .leftJoin(
        communityToEventSchema,
        eq(communityToEventSchema.eventId, eventSchema.id),
      )
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .leftJoin(
        permissionUserToRoleSchema,
        and(
          eq(permissionUserToRoleSchema.subjectId, eventSchema.id),
          eq(permissionUserToRoleSchema.subject, "event"),
          eq(permissionUserToRoleSchema.userId, loginData.id),
        ),
      )
      .where(and(selectWhereBase, selectWhereCommunityAndPeriod))

    const selectCommunityOrBlank = input.communityId
      ? or(inArray(communityToEventSchema.communityId, userCommunities))
      : undefined

    const selectWhereUserCommunityAndPeriod = and(
      selectCommunityOrBlank,
      selectPeriod,
    )

    const myHostedEventsWhere = and(
      eq(permissionUserToRoleSchema.subject, "event"),
      eq(permissionUserToRoleSchema.userId, loginData.id),
      or(
        eq(permissionUserToRoleSchema.roleId, "host"),
        eq(permissionUserToRoleSchema.roleId, "cohost"),
      ),
      selectWhereUserCommunityAndPeriod,
    )

    const myInteractedEventsWhere = and(
      eq(permissionUserToRoleSchema.subject, "event"),
      eq(permissionUserToRoleSchema.userId, loginData.id),
      selectWhereUserCommunityAndPeriod,
    )

    const myEvents = db
      .select(selectEvents)
      .from(permissionUserToRoleSchema)
      .innerJoin(
        eventSchema,
        eq(permissionUserToRoleSchema.subjectId, eventSchema.id),
      )
      .leftJoin(
        communityToEventSchema,
        and(
          eq(
            communityToEventSchema.eventId,
            permissionUserToRoleSchema.subjectId,
          ),
          inArray(communityToEventSchema.communityId, userCommunities),
        ),
      )
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .where(
        input.owned === "host" ? myHostedEventsWhere : myInteractedEventsWhere,
      )

    const eventsAll = union(eventsCommon, myEvents)

    let eventRequest: typeof myEvents | typeof eventsCommon | typeof eventsAll

    switch (input.owned) {
      case "host":
      case "my":
        eventRequest = myEvents
        break
      case "all":
      default:
        eventRequest = eventsAll
    }

    const events = await eventRequest
      .orderBy(eventSchema.starts)
      .then(async (events) => {
        return await Promise.all(
          events.map(async (event) => {
            const communities = await getEventCommunities({
              eventId: event.id,
              userCommunities,
            })

            const responseEvent = {
              ...event,
              communities,
            }

            const canView = hasPermission(
              loginData,
              "event",
              "viewPublic",
              responseEvent,
            )

            const canJoin = hasPermission(
              loginData,
              "event",
              "join",
              responseEvent,
            )

            return {
              ...responseEvent,
              canJoin,
              canView,
            }
          }),
        )
      })

    return events.filter((event) => event.canView)
  })
