import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { checkLogin } from "../checkLogin"
import { t } from "../trpc"

export const publicEventProcedure = t.procedure
  .input(z.object({ eventId: z.number() }))
  .use(async function checkEventAccess(opts) {
    const { eventId } = opts.input

    if (!eventId) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No Event Selected",
      })
    }

    // Get event data first
    const event = await db
      .select({
        id: eventSchema.id,
        openness: eventSchema.openness,
        status: eventSchema.state,
        memberApproval: eventSchema.memberApproval,
        share: eventSchema.share,
        allowInfiniteOrganizers: eventSchema.allowInfiniteOrganizers,
      })
      .from(eventSchema)
      .where(eq(eventSchema.id, eventId))
      .then(async (event) => {
        if (!event[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Such event does not exist",
          })
        }

        const communities = await db
          .select({
            id: communityToEventSchema.communityId,
            owner: communityToEventSchema.owner,
          })
          .from(communityToEventSchema)
          .where(eq(communityToEventSchema.eventId, event[0].id))
          .then((communities) => communities)

        return { ...event[0], communities, hosts: communities }
      })

    if (!event) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Such event does not exist",
      })
    }

    // Check if user is authenticated
    let loginData = null
    if (opts.ctx.auth?.payload?.sub) {
      try {
        loginData = await checkLogin(opts.ctx.auth.payload.sub)
      } catch (error) {
        // User authentication failed, treat as unregistered
        loginData = null
      }
    }

    // Create user object for permission checking
    const user = loginData
      ? loginData
      : {
          id: null,
          roles: [],
        }

    // Check if user can view this event publicly
    if (!hasPermission(user, "event", "viewPublic", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You have no access to this event",
      })
    }

    return opts.next({
      ctx: {
        auth: opts.ctx.auth,
        loginData,
        event,
        isAuthenticated: !!loginData,
      },
    })
  })
