<?php
function getExpansionList($database, $gameId, $eventId, $settings)
{
    $expansions = $database->query("
    SELECT
        `id`,
        `title`,
        `type`,
        `bgg_id`
    FROM 
        `game2expansion`
    INNER JOIN `games` ON `game2expansion`.`expansion_id` = `games`.`id`
    WHERE
        `game_id` = " . $gameId . "
    AND
        `visible` = '1'
    ")->fetchALl();

    $expansionUsers = [];
    $expansionTags = [];

    foreach ($expansions as $expansion) {
        $users = $database->query("
        SELECT
            `user2game`.`user_id`,
            `user2game`.`cal_last_play`,
            `user2game`.`cal_play_count`,
            `user2game`.`rating`,
            `user2game`.`news`,
            `games`.`type`,
            `games`.`bgg_id`
        FROM
            `user2game`
        INNER JOIN 
            `games` ON `user2game`.`game_id` = `games`.`id`
        INNER JOIN 
            `user2event` ON `user2event`.`user_id` = `user2game`.`user_id`
            AND `user2event`.`event_id` = " . $eventId . "
        WHERE
            `user2event`.`share_games` = 1
        AND
            `user2game`.`game_id` = " . $expansion['id'])->fetchAll();

        if ($users) {
            $registerDuplicates = [];

            foreach ($users as $user) {
                if (!isset($registerDuplicates[$user['user_id']])) {
                    $registerDuplicates[$user['user_id']] = true;
                    if (!isset($expansionUsers[$user['user_id']])) {
                        $expansionUsers[$user['user_id']] = ['last_play' => 0,
                            'rating' => 0,
                            'rating_count' => 0,
                            'play_count' => 0,
                            'exp_count' => 0,
                            'news' => 0,
                            'base_rating' => 0,
                            'base_id' => null,
                            'true_base' => false];
                    }

                    $userData = $expansionUsers[$user['user_id']];

                    $expansionUsers[$user['user_id']]['exp_count'] = $userData['exp_count'] + 1;

                    $lastPlayGame = $user['cal_last_play'] ? strtotime($user['cal_last_play']) : 0;

                    if ($lastPlayGame > $userData['last_play']) {
                        $expansionUsers[$user['user_id']]['last_play'] = $lastPlayGame;
                    }

                    if (!empty($user['rating']) && $user['rating'] > 0) {
                        $expansionUsers[$user['user_id']]['rating'] = $userData['rating'] + intval($user['rating']);
                        $expansionUsers[$user['user_id']]['rating_count'] = $userData['rating_count'] + 1;
                    }

                    if ($user['type'] === "base_expansion" || $user['type'] === "base") {
                        if (!$userData['true_base']) {
                            if ($userData['base_id'] === null || $userData['base_id'] > $user['bgg_id']) {
                                $expansionUsers[$user['user_id']]['base_id'] = $user['bgg_id'];
                                $expansionUsers[$user['user_id']]['true_base'] = $user['type'] === 'base';
                                $expansionUsers[$user['user_id']]['base_rating'] = $user['rating'];
                            }
                        }
                    }

                    $expansionUsers[$user['user_id']]['play_count'] = $userData['play_count'] + intval($user['cal_play_count']);

                    $newDate = strtotime($user['news']);
                    if ($newDate > time() - (findSetting($settings['settings'], 'games:news_before') * 60 * 60 * 24)) {
                        $expansionUsers[$user['user_id']]['news'] = $userData['news'] + 1;
                    }
                }
            }

            $tags = $database->query("
                SELECT
                    `id`
                FROM 
                    `game2tag`
                INNER JOIN `tags` ON `game2tag`.`tag_id` = `tags`.`id`
                WHERE
                    `type_id` != " . findSetting($settings['settings'], "tags:expansion_tag_type_id") . "
                AND
                    `tags`.`visible` = 1
                AND            
                    `game2tag`.`game_id` = " . $expansion['id'])->fetchAll();

            array_push($expansionTags, ...$tags);
        }
    }

    $expansionTagsNormalized = array_unique($expansionTags, SORT_REGULAR);

    return ['tags' => $expansionTagsNormalized, 'users' => $expansionUsers, 'expansions' => $expansions];
}