<?php

function saveGameData($database, $gameData, $gameId, $eventId, $expansions, $settings)
{
    // select existing event data for game
    $link = $database->select("agr_game2event", ["game_id",
        "agr_user_data",
        "agr_expansion_data"], ["game_id" => $gameId,
        "event_id" => $eventId]);

    $shouldUpdateTimestamp = false;

    $mergedUserData = getUserExperienceScore($gameData, $expansions['users'], $settings);
    fillUser2Base($database, $expansions['users'], $gameId);

    $gameUserData = [];
    // prep insert data per user
    foreach ($mergedUserData as $userId => $user2gameData) {
        $gameUserData[] = [$userId, $user2gameData['score'], $user2gameData['status']];
        /*
        if ($user2gameData['news']) {
            $shouldUpdateTimestamp = true;
        }
        */
    }

    $userCount = count($mergedUserData);

    $tagData = [];
    foreach ($expansions['tags'] as $tag) {
        $tagData[] = $tag['id'];
    }

    $expansionData = [];
    foreach ($expansions['expansions'] as $expansion) {
        $expansionData[] = $expansion['title'];
    }

    $gameResultData = ['agr_user_data' => json_encode($gameUserData),
        'agr_tag_data' => json_encode($tagData),
        'agr_expansion_data' => json_encode($expansionData),
        'user_count' => count($gameUserData)];

    if (!empty($link)) {
        if ($link[0]['agr_user_data'] != $gameResultData['agr_user_data'] || $link[0]['agr_expansion_data'] != $gameResultData['agr_expansion_data']) {
            $gameResultData['news'] = date("Y-m-d H:i:s", time());
        }


        $database->update("agr_game2event", $gameResultData, ["game_id" => $gameId,
            "event_id" => $eventId]);
    } else {
        $database->insert("agr_game2event", [...$gameResultData,
            "news" => date("Y-m-d H:i:s", time()),
            "game_id" => $gameId,
            "event_id" => $eventId]);
    }
}
