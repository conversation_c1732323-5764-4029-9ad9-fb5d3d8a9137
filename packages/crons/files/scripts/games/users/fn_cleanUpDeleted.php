<?php

function cleanUpDeleted($database, $doc, $user)
{
    $currentGames = $database->select('user2game', ['bgg_collid', 'game_id'], ['user_id' => $user['id'],
        'deleted' => 0]);

    if (empty($currentGames)) {
        return;
    }

    foreach ($currentGames as $game) {
        $gameFound = false;
        foreach ($doc->item as $item) {
            if ($item['collid'] == $game['bgg_collid']) {
                $gameFound = true;
                break;
            }
        }
        if (!$gameFound) {
            $database->update('user2game', ['deleted' => 1], ['bgg_collid' => $game['bgg_collid']]);
            $database->update('user2base_game', ['deleted' => 1], ['game_id' => $game['game_id'], $user['id']]);
        }
    }
}