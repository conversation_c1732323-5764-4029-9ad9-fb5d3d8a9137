@value isMobile: 576px;
@value isTablet: 768px;
@value isLargeTablet: 1024px;
@value isSmallDesktop: 1280px;
@value isDesktop: 1440px;

html,
body {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}

:root {
    --spacing-q: 2px;
    --spacing-h: 4px;
    --spacing-1: 8px;
    --spacing-2: 16px;
    --spacing-3: 24px;
    --spacing-4: 32px;
    --spacing-5: 40px;
    --spacing-6: 48px;
    --spacing-7: 56px;
    --spacing-8: 64px;
    --spacing-9: 72px;
    --spacing-10: 80px;
    --spacing-11: 88px;
    --spacing-12: 96px;
                --spacing-13: 104px;
    --spacing-14: 112px;
    --spacing-15: 120px;
    --spacing-16: 128px;
    --spacing-17: 134px;
    --spacing-18: 142px;
    --currentBreakpoint: if(
        media(width < isMobile): "mobile";
        media(width < isTablet): "tablet";
        media(width < isLargeTablet): "largeTablet";
        media(width < isSmallDesktop): "smallDesktop";
        media(width < isDesktop): "desktop";
        else: "largeDesktop";
    )
)
}

h1 {
    margin: 0;
    font-weight: bold;
    font-size: 2.4292rem;
}

h2 {
    margin: 0;
    font-weight: 500;
    font-size: 2.125rem;
    letter-spacing: 0.00735em;
}

h3 {
    margin: 0;
    font-weight: 500;
    font-size: 1.5rem;
    letter-spacing: 0.00735em;
}

h4 {
    margin: 0;
    font-weight: 400;
    font-size: 1rem;
    letter-spacing: 0.00938em;
}

h5 {
    margin: 0;
    font-weight: 400;
    font-size: 1rem;
    letter-spacing: 0.00938em;
}

h6 {
    margin: 0;
    font-weight: 400;
    font-size: 1rem;
    letter-spacing: 0.00938em;
}