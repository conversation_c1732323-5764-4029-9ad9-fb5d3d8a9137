import {
  Box,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid2,
  Link,
  Switch,
  Typography,
} from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import { useCallback } from "react"
import { z } from "zod"

import { LoadingButtonWithError } from "../../../../components/elements/LoadingButtonWithError/LoadingButtonWithError"
import { UserAvatar } from "../../../../components/user/UserAvatar/UserAvatar"
import {
  type RoleData,
  hasEventRole,
  hasPermission,
} from "../../../../permissions"
import { EVENT_PARTICIPANTS_ROUTE, INDEX_ROUTE } from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { useUserStore } from "../../../../store/useUserStore"
import { trpc } from "../../../../trpc/trpc"
import { EventWizzardState } from "../../../../types/types"

import * as styles from "./userInfo.module.css"

interface UserInfoProps {
  user: {
    id: number
    name: string
    bggUsername: string | null
    roles: RoleData[]
    avatar: string | null
    color: string | null
    wizzardState: EventWizzardState | null
    canRequest: boolean | null
    share: boolean | null
  }
  canApprove: boolean
  eventId: number
  canShare: boolean
}

const schema = z.object({
  eventId: z.number(),
  userId: z.number(),
  removeRole: z.boolean().optional(),
  status: z.enum([
    "participant",
    "interested",
    "requested",
    "reserved",
    "notgoing",
    "unwelcome",
    "cohost",
  ]),
})

export const UserInfo = ({
  user,
  eventId,
  canApprove,
  canShare,
}: UserInfoProps) => {
  const myData = useUserStore((store) => store.userData)
  const router = useRouter()
  const { setTrpcError } = useToastStore()
  const onSubmit = useCallback(
    async (status: string, removeRole: boolean = false) => {
      try {
        const data = schema.parse({
          eventId,
          userId: user.id,
          status: status,
          removeRole: removeRole,
        })
        await trpc.event.participant.do.updateStatus.mutate(data)
        router.invalidate()
        return false
      } catch (error: unknown) {
        setTrpcError(error)
        return false
      }
    },
    [user.id, eventId, router],
  )

  const handleChange = useCallback(
    async (option: "share" | "canRequest", value: boolean) => {
      try {
        await trpc.event.wizzard.do.updateSettings.mutate({
          eventId: eventId,
          share: option === "share" ? value : (user.share ?? false),
          canRequest:
            option === "canRequest" ? value : (user.canRequest ?? true),
        })
        router.invalidate()
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [user.share, eventId, router, setTrpcError, user.canRequest],
  )

  const onLeave = useCallback(async () => {
    try {
      const leave = await trpc.protected.user.do.updateEventStatus
        .mutate({ eventId, status: "notgoing" })
        .then(() => true)
        .catch(() => false)
      if (leave) {
        router.invalidate()
        router.navigate({
          to: myData.id === user.id ? INDEX_ROUTE : EVENT_PARTICIPANTS_ROUTE,
          params: {
            eventId: String(eventId),
          },
        })
        return true
      } else {
        return false
      }
    } catch (error: unknown) {
      setTrpcError(error)
      return false
    }
  }, [user.id, eventId, router, myData.id, setTrpcError])

  const statusError = "Failed to update status"

  const isParticipant = hasPermission(user, "event", "isParticipant", {
    id: eventId,
  })

  const isDenied = hasPermission(user, "event", "isDenied", {
    id: eventId,
  })

  const isRequested = hasPermission(user, "event", "isRequested", {
    id: eventId,
  })

  const isReserved = hasPermission(user, "event", "isRequested", {
    id: eventId,
  })

  const isInterested = hasPermission(user, "event", "isWatching", {
    id: eventId,
  })

  const canIBan =
    hasPermission(myData, "event", "approve", {
      id: eventId,
    }) && user.id !== myData?.id

  const displayOtherFields = !(user.share ?? false)

  return (
    <Box className={styles.container}>
      <Box>
        <Box>
          <UserAvatar user={user} size="large" labelInfo="Member: " />
        </Box>
      </Box>
      <Box maxWidth={400}>
        <Grid2 columns={2} padding={2} container spacing={2}>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Name
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography>{user.name}</Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Status
            </Typography>
          </Grid2>
          {!isDenied && (
            <Grid2 size={1}>
              <Typography variant="body1" fontWeight={500}>
                {!isParticipant ? "In Waiting list" : "Participant"}
              </Typography>
            </Grid2>
          )}
          {canShare && (
            <>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      onChange={() => handleChange("share", !user.share)}
                      checked={user.share ?? false}
                    />
                  }
                  label="I want to add games to this event"
                />
              </FormGroup>
              <FormControl disabled={displayOtherFields}>
                <FormControlLabel
                  control={
                    <Switch
                      onChange={() =>
                        handleChange("canRequest", !user.canRequest)
                      }
                      checked={user.canRequest ?? false}
                    />
                  }
                  label="By default people can request any game from my collection"
                />
              </FormControl>
            </>
          )}
          {user.bggUsername && (
            <>
              <Grid2 size={1}>
                <Typography variant="body1" fontWeight={500}>
                  BGG Link
                </Typography>
              </Grid2>
              <Grid2 size={1}>
                <Typography>
                  <Link
                    target="_blank"
                    href={`https://boardgamegeek.com/user/${user.bggUsername}`}
                    underline="none"
                  >
                    Open
                  </Link>
                </Typography>
              </Grid2>
            </>
          )}
          {canApprove && user.id !== myData?.id && (
            <>
              <Grid2 size={2}>
                <Typography variant="body1" fontWeight={500}>
                  Manage participation status
                </Typography>
              </Grid2>
              <Grid2 size={1}>
                <Box
                  gap={1}
                  display="flex"
                  justifyContent="center"
                  flexDirection="column"
                >
                  {(isRequested || isReserved) && canApprove && (
                    <>
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("participant")}
                        errorMessage={statusError}
                        title="Approve"
                        color="primary"
                        variant="contained"
                      />
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("notgoing")}
                        errorMessage={statusError}
                        title="Reject"
                        color="secondary"
                        variant="contained"
                      />
                    </>
                  )}
                  {isInterested && canApprove && (
                    <LoadingButtonWithError<boolean>
                      onClick={async () => await onSubmit("participant")}
                      errorMessage={statusError}
                      title="Make Participant"
                      color="primary"
                      variant="contained"
                    />
                  )}
                  {canIBan && (
                    <LoadingButtonWithError<boolean>
                      onClick={async () => await onSubmit("unwelcome")}
                      errorMessage={statusError}
                      title="Ban"
                      color="error"
                      displayConfirm
                      confirmMessage="Do You really want to ban this person from Your event?"
                      variant="contained"
                    />
                  )}
                  {hasPermission(myData, "event", "promoteCohost", {
                    id: eventId,
                  }) &&
                    (hasEventRole(user.roles, "cohost", eventId) ? (
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("cohost", true)}
                        errorMessage={statusError}
                        title="Remove Co-Host Status"
                        color="error"
                        variant="contained"
                        successMessage={`Co-host status removed for ${user.name}`}
                      />
                    ) : (
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("cohost")}
                        errorMessage={statusError}
                        title="Set as Co-Host"
                        color="error"
                        variant="contained"
                        successMessage={`Co-host status set for ${user.name}`}
                      />
                    ))}
                </Box>
              </Grid2>
            </>
          )}
          {user.id === myData?.id && (
            <Grid2 size={2}>
              <LoadingButtonWithError
                title="Cancel Participation"
                onClick={onLeave}
                color="error"
                variant="contained"
                displayConfirm
                confirmMessage="Do You really want to cancel Your participation?"
                successMessage="You have cancelled Your participation"
                errorMessage="Failed to leave event"
              />
            </Grid2>
          )}
        </Grid2>
      </Box>
    </Box>
  )
}
