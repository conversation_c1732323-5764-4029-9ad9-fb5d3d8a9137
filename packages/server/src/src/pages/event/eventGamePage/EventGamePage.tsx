import { Box, Button, Typography } from "@mui/material"
import { useN<PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { BggLink } from "../../../components/games/BggLink/BggLink"
import { GameInfo } from "../../../components/games/GameInfo/GameInfo"
import { GAME_IMAGES } from "../../../config/images"
import { eventGameRoute } from "../../../routes/event/eventGame.route"
import {
  EVENT_GAMES_ROUTE,
  EVENT_GAME_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import { Chips } from "./components/Chips"
import { ExpansionList } from "./components/ExpansionList"
import { Owners } from "./components/Owners"
import { WillBringGame } from "./components/WillBringGame"
import * as styles from "./eventGamePage.module.css"

const backLinks: Record<string, string> = {
  eventGames: EVENT_GAMES_ROUTE,
}

export const EventGamePage = () => {
  const base = useParentRouteData(EVENT_ROOT_ROUTE)
  const game = eventGameRoute.useLoaderData()
  const search = eventGameRoute.useSearch()
  const params = eventGameRoute.useParams()
  const navigate = useNavigate()
  const myUserId = useUserStore((state) => state.userData.id)

  const router = useRouter()

  const passedParams: Record<string, string> = useMemo(() => {
    return search.sourceParams ? JSON.parse(search.sourceParams) : {}
  }, [search.sourceProps])

  const onViewUser = useCallback(
    (id?: number) =>
      navigate({
        to: EVENT_GAME_ROUTE,
        params: {
          eventId: params.eventId,
          gameId: params.gameId,
        },
        search: {
          userId: id,
          hideOthers: search.hideOthers,
        },
      }),
    [navigate, params, search],
  )

  const handleBack = useCallback(() => {
    navigate({
      to: backLinks[search.sourcePage ?? "eventGames"],
      params: {
        eventId: params.eventId,
        ...passedParams,
      },
      search: search.sourceProps ? JSON.parse(search.sourceProps) : {},
    })
  }, [router])

  if (!isEvent(base) || !game) return null

  return (
    <Box pb={8}>
      <TitleRow
        title={
          <Box display="flex" flexDirection="row" gap={2}>
            <Button variant="text" onClick={handleBack}>
              {`<- Back`}
            </Button>
            {game.game.title}
          </Box>
        }
      ></TitleRow>
      <Box>
        <WillBringGame />
      </Box>
      {!game.game.share && game.game.userOwns && (
        <Box>
          <Typography variant="h6">You are not sharing Your games</Typography>
          <PartyLink
            to={EVENT_PARTICIPANT_ROUTE}
            params={{
              participantId: String(myUserId),
              eventId: params.eventId,
            }}
            variant="contained"
            color="secondary"
          >
            Change in profile
          </PartyLink>
        </Box>
      )}
      <Box className={styles.firstRowWrapper}>
        <Box className={styles.gameInfoWrapper}>
          <Box display="flex" justifyContent="center">
            <Box className={styles.imageBox}>
              <img
                src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.game.id}.jpg`}
                alt={game.game.title}
              />
              <Box className={styles.link}>
                <BggLink bggId={game.game.bggId} />
                {game.game.average && (
                  <Box className={styles.bggRating}>
                    <Typography fontWeight={600}>
                      {Math.round(game.game.average * 10) / 10}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
          <GameInfo game={game.game} />
        </Box>
        <Owners
          eventId={base?.id ?? 0}
          search={search}
          users={game.game.users}
          onViewUser={onViewUser}
        />
      </Box>
      <Box className={styles.borderBox} mt={2}>
        <Chips tagList={game.tags} />
      </Box>
      {game.expansions.length > 0 && (
        <Box pt={2} className={styles.borderBox}>
          <ExpansionList
            users={game.game.users}
            expansions={game.expansions}
            onViewUser={onViewUser}
          />
        </Box>
      )}
    </Box>
  )
}
