import {
  Box,
  FormControl,
  FormControlLabel,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Switch,
} from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import { useCallback, useState } from "react"

import { eventWizzardRoute } from "../../../../routes/event/eventWizzard.route"
import { EVENT_ROOT_ROUTE } from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import { ActionBar } from "./ActionBar"

export const Settings = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const [preset, setPreset] = useState<number | null>(null)
  const router = useRouter()
  const settings = eventWizzardRoute.useLoaderData()
  const { setTrpcError } = useToastStore()

  if (!event || !isEvent(event) || !settings) {
    return null
  }

  const handleChange = useCallback(
    async (option: "share" | "canRequest", value: boolean) => {
      try {
        await trpc.event.wizzard.do.updateSettings.mutate({
          eventId: event.id,
          share: option === "share" ? value : (settings.share ?? false),
          canRequest:
            option === "canRequest" ? value : (settings.canRequest ?? true),
        })
        router.invalidate()
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [settings.share, event.id, router, setTrpcError, settings.canRequest],
  )

  const handleSelectPreset = useCallback(
    async (e: SelectChangeEvent<unknown>) => {
      if (e.target.value === -1) {
        setPreset(null)
        return
      }
      setPreset(e.target.value as number)
    },
    [event.id, setPreset],
  )

  const displayOtherFields = !(settings.share ?? false)

  return (
    <Box display="flex" justifyContent="center" alignItems="center" pt={4}>
      <Box display="flex" flexDirection="column" gap={2} maxWidth={500}>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                onChange={() => handleChange("share", !settings.share)}
                checked={settings.share ?? false}
              />
            }
            label="I want to add games to this event"
          />
        </FormGroup>
        <FormControl disabled={displayOtherFields}>
          <FormControlLabel
            control={
              <Switch
                onChange={() =>
                  handleChange("canRequest", !settings.canRequest)
                }
                checked={settings.canRequest ?? false}
              />
            }
            label="By default people can request any game from my collection"
          />
        </FormControl>
        {settings.presets.length > 0 && (
          <FormControl disabled={displayOtherFields}>
            <InputLabel id={`presets-label-id`}>
              Select game list preset
            </InputLabel>
            <Select
              label="Select game list preset"
              labelId={`presets-label-id`}
              onChange={handleSelectPreset}
              defaultValue={-1}
            >
              <MenuItem value={-1} key="none">
                None
              </MenuItem>
              {settings.presets.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      </Box>
      <ActionBar
        eventId={event.id}
        noPrevious
        presetId={preset}
        isDone={!settings.share}
      />
    </Box>
  )
}
