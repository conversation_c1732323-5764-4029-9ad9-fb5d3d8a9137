import { Box } from "@mui/material"
import { useN<PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import { useCallback, useEffect, useMemo } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import {
  GameSearch,
  type SearchParams,
} from "../../../components/games/GameSearch/GameSearch"
import { GamesThumbnailView } from "../../../components/games/GamesThumbnailView/GamesThumbnailView"
import { SearchModal } from "../../../components/modals"
import { eventGamesRoute } from "../../../routes/event/eventGames.route"
import {
  EVENT_GAMES_ROUTE,
  EVENT_GAME_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../routes/paths"
import { isOrderBy, useGameStore } from "../../../store/useGamesStore"
import { useIsMobileStore } from "../../../store/useIsMobileStore"
import { useToastStore } from "../../../store/useToastStore"
import { trpc } from "../../../trpc/trpc"
import { EventGameStatusReset } from "../../../types/types"
import { applyFilters } from "../../../utils/filter"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import * as styles from "./eventGamesPage.module.css"

export const EventGamesPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const navigate = useNavigate({ from: EVENT_GAMES_ROUTE })
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const search = eventGamesRoute.useSearch()
  const linkParams = eventGamesRoute.useParams()
  const router = useRouter()
  const { setTrpcError, setMessage } = useToastStore()

  const { getGames, setMeta } = useGameStore()

  const games = getGames("event", parseInt(linkParams.eventId))

  useEffect(() => {
    const order = search.order === "desc" ? "desc" : "asc"
    const orderBy =
      search.orderBy && isOrderBy(search.orderBy) ? search.orderBy : "title"
    setMeta({
      search: search.search ?? "",
      order,
      minPlayers: search.minPlayers,
      maxPlayers: search.maxPlayers,
      playerLevel: search.playerLevel ?? 1,
      orderBy,
      id: parseInt(linkParams.eventId),
      filter: search.filter,
      type: "event",
    })
  }, [search, linkParams.eventId])

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: EVENT_PARTICIPANT_ROUTE,
        params: {
          participantId: String(id),
        },
      }),
    [navigate],
  )

  const handleOnStatusChange = useCallback(
    async (status: EventGameStatusReset, gameId: number) => {
      try {
        await trpc.event.games.do.updateStatus.mutate({
          eventId: Number(linkParams.eventId),
          gameId,
          status,
        })
        router.invalidate()
        setMessage({
          message:
            "Game status updated. Remember: it will take few minutes for changes in event game list.",
          code: "DONE",
          severity: "success",
        })
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [linkParams.eventId, setMessage, setTrpcError, router],
  )

  if (!isEvent(event) || !games) {
    return null
  }

  const useGameList = useMemo(() => {
    return applyFilters(
      games.games,
      games.meta,
      games.populatedTags,
      games.populatedUsers,
      "event-willbring",
    )
  }, [games])

  const onNavigateGame = useCallback(
    (search: SearchParams) => {
      navigate({
        to: EVENT_GAMES_ROUTE,
        search,
        params: linkParams,
      })
    },
    [navigate, linkParams],
  )

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
          minPlayers: games.meta.minPlayers,
          maxPlayers: games.meta.maxPlayers,
          playerLevel: games.meta.playerLevel,
          search: games.meta.search ?? "",
          order: games.meta.order,
          orderBy: games.meta.orderBy,
          filter: games.meta.filter,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [games.meta, navigate],
  )

  const titleRow = useMemo(() => {
    return <TitleRow title="Event Games" />
  }, [linkParams.eventId])

  const isLargeTablet = sizeThresholdList.largeTablet

  return (
    <>
      <Box className={styles.header}>
        <Box className={styles.searchBar}>
          <GameSearch
            onNavigate={onNavigateGame}
            search={search}
            tags={games.tags}
            tagCategories={games.tagCategories}
            enableFilters="event"
          />
          <SearchModal
            onNavigate={onNavigateGame}
            search={search}
            tags={games.tags}
            tagCategories={games.tagCategories}
          />
        </Box>
        {titleRow}
      </Box>
      <GamesThumbnailView
        listMode={isLargeTablet}
        navigation={{
          to: EVENT_GAME_ROUTE,
          params: {
            eventId: linkParams.eventId.toString(),
          },
          search: {
            sourcePage: "games",
            sourceProps: JSON.stringify(search),
          },
        }}
        type="event"
        games={useGameList}
        onUser={onClick}
        onPageChange={onChange}
        page={search.page ?? 1}
        onEventStatusChange={handleOnStatusChange}
      />
    </>
  )
}
