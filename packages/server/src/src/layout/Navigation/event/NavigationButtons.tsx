import { Box } from "@mui/material"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { hasPermission } from "../../../permissions"
import { eventRootRoute } from "../../../routes/event/event.root.route"
import {
  EVENT_EDIT_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_ORGANIZERS_ROUTE,
  EVENT_PARTICIPANTS_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROUTE_INFO,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
export const NavigationButtons = () => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const userInfo = useUserStore((state) => state.userData)

  const eventData = eventRootRoute.useLoaderData()

  const id = String(eventData?.id ?? 0)

  return (
    <>
      <Box justifyContent="space-between" display="flex" gap={2} width="100%">
        <Box display="flex" gap={2}>
          {isLoggedIn && (
            <>
              <PartyLink
                color="inherit"
                size="large"
                sx={{ fontSize: "1.25rem" }}
                variant="text"
                to={EVENT_ROUTE_INFO}
                params={{ eventId: id }}
              >
                {eventData?.title ?? ""}
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={EVENT_GAMES_ROUTE}
                params={{ eventId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Games
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={EVENT_PARTICIPANTS_ROUTE}
                params={{ eventId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Participants
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={EVENT_ORGANIZERS_ROUTE}
                params={{ eventId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Organizers
              </PartyLink>
              {hasPermission(userInfo, "event", "update", {
                id: Number(id),
              }) && (
                <PartyLink
                  color="inherit"
                  variant="text"
                  to={EVENT_EDIT_ROUTE}
                  params={{ eventId: id }}
                  preload="intent"
                  preloadDelay={500}
                >
                  Administrate
                </PartyLink>
              )}
              <PartyLink
                to={EVENT_PARTICIPANT_ROUTE}
                params={{
                  participantId: String(userInfo?.id ?? 0),
                  eventId: id,
                }}
                color="inherit"
                variant="text"
              >
                Profile
              </PartyLink>
            </>
          )}
        </Box>

        <Box display="flex" gap={2}>
          <PartyLink to={INDEX_ROUTE} color="inherit" variant="text">
            Home
          </PartyLink>
          <LoginButton />
        </Box>
      </Box>
    </>
  )
}
