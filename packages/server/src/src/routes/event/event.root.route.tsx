import { Outlet, createRoute, redirect } from "@tanstack/react-router"
import { TanStackRouterDevtools } from "@tanstack/router-devtools"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LayoutEvent } from "../../layout/LayoutEvent/LayoutEvent"
import { Navigation } from "../../layout/Navigation/Navigation"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { GeneralErrorPage } from "../../pages/common/generalErrorPage/GeneralErrorPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { EVENT_ROOT_ROUTE, INDEX_ROUTE } from "../paths"
import { rootRoute } from "../root"

export const eventRootRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: EVENT_ROOT_ROUTE,
  staleTime: EVENT_STALE_TIME,
  beforeLoad: ({ context: { userData } }) => {
    // Allow both authenticated and unauthenticated users
    // Public events can be viewed without authentication
  },
  loader: async ({ context: { trpc, userData }, params: { eventId } }) => {
    try {
      // Try public event query first (works for both authenticated and unauthenticated)
      return await trpc.public.event.basic.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      // If public query fails and user is authenticated, try protected query
      if (userData?.isLoggedIn) {
        try {
          return await trpc.protected.event.basic.query({
            eventId: parseInt(eventId),
          })
        } catch (protectedError) {
          return handleLoaderErrors("Event not found!", protectedError)
        }
      }
      return handleLoaderErrors("Event not found!", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  notFoundComponent: (data) => (
    <>
      <Navigation pad type="event" />
      <LayoutEvent>
        <GeneralErrorPage data={data} />
      </LayoutEvent>
      {ENV_MODE === "dev" && <TanStackRouterDevtools />}
    </>
  ),
  component: () => {
    return (
      <>
        <Navigation pad type="event" />
        <LayoutEvent>
          <Outlet />
        </LayoutEvent>
        {ENV_MODE === "dev" && <TanStackRouterDevtools />}
      </>
    )
  },
})
